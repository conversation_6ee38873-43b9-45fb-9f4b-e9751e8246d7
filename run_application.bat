@echo off
title School Management System - Launcher
color 0B

echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                                                              ║
echo     ║           🎓 نظام إدارة طلاب المدرسة المتطور 🎓              ║
echo     ║                                                              ║
echo     ║                School Management System v2.0                 ║
echo     ║                                                              ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if release build exists
if exist "build-release\dist\SchoolManagementSystem.exe" (
    echo ✅ Found Release Version
    echo 🚀 Starting School Management System...
    echo.
    echo ╔═══════════════════════════════════════╗
    echo ║           🔐 Login Information         ║
    echo ╠═══════════════════════════════════════╣
    echo ║ 👤 Username: admin                    ║
    echo ║ 🔒 Password: admin123                 ║
    echo ╚═══════════════════════════════════════╝
    echo.
    
    cd build-release\dist
    start SchoolManagementSystem.exe
    cd ..\..
    
    echo ✨ Application launched successfully!
    echo 💡 The application is now running in the background.
    echo 🔍 Check your system tray for the application icon.
    echo.
    
) else if exist "build\bin\SchoolManagementSystem.exe" (
    echo ⚠️  Using Debug Version
    echo 🚀 Starting School Management System (Debug)...
    echo.
    echo ╔═══════════════════════════════════════╗
    echo ║           🔐 Login Information         ║
    echo ╠═══════════════════════════════════════╣
    echo ║ 👤 Username: admin                    ║
    echo ║ 🔒 Password: admin123                 ║
    echo ╚═══════════════════════════════════════╝
    echo.
    
    cd build\bin
    start SchoolManagementSystem.exe
    cd ..\..
    
    echo ✨ Debug application launched!
    echo.
    
) else (
    echo ❌ Application not found!
    echo.
    echo 🔨 Please build the application first:
    echo    • For development: run build_simple.bat
    echo    • For release: run build_release.bat
    echo.
    echo 📁 Expected locations:
    echo    • Release: build-release\dist\SchoolManagementSystem.exe
    echo    • Debug: build\bin\SchoolManagementSystem.exe
    echo.
    pause
    exit /b 1
)

echo 🎉 Enjoy using the School Management System!
echo.
echo 💡 Tips:
echo    • Use the system tray icon to show/hide the application
echo    • All data is automatically saved
echo    • Check the settings for customization options
echo.

timeout /t 5 >nul
