# 🎓 نظام إدارة طلاب المدرسة - الإصدار المتطور v2.0

نظام شامل ومتطور لإدارة بيانات طلاب المدرسة مبني بـ C++ و Qt6 مع واجهة مستخدم احترافية وتقنيات حديثة.

## ✨ المميزات المتطورة الجديدة

### 🚀 **تطبيق سطح مكتب احترافي**
- **شاشة ترحيب متحركة**: Splash Screen مع شريط تقدم وتأثيرات
- **إدارة تطبيق متقدمة**: ApplicationManager للتحكم الكامل
- **أيقونة في شريط المهام**: System Tray مع قائمة سياق
- **إعدادات قابلة للحفظ**: حفظ تفضيلات المستخدم تلقائياً
- **دعم متعدد الثيمات**: ثيم فاتح وداكن

### 🎨 **واجهة مستخدم متطورة**
- **تصميم Material Design**: واجهة عصرية مع تدرجات لونية جميلة
- **تأثيرات بصرية متقدمة**: ظلال ثلاثية الأبعاد وانتقالات سلسة
- **نافذة تسجيل دخول بلا إطار**: تصميم نظيف مع خلفية متدرجة شفافة
- **كروت وبطاقات ذكية**: تنظيم أفضل للمحتوى مع تأثيرات الظل
- **أيقونات تعبيرية**: استخدام الرموز التعبيرية لتحسين تجربة المستخدم

### 🔐 **نظام أمان وحماية متقدم**
- **تشفير كلمات المرور**: حماية بـ SHA-256
- **حماية من SQL Injection**: استخدام Prepared Statements
- **التحقق من صحة البيانات**: فلترة وتنظيف المدخلات
- **جلسات آمنة**: إدارة جلسات المستخدم بأمان

### 📊 **إدارة شاملة ومتطورة للطلاب**
- **عرض ديناميكي**: جدول تفاعلي مع عداد الطلاب المباشر
- **بحث فوري ذكي**: نتائج فورية أثناء الكتابة
- **نماذج ذكية**: حقول مع placeholders وتلميحات تفاعلية
- **أزرار تفاعلية**: تصميم متدرج مع تأثيرات حركية
- **تصدير واستيراد البيانات**: دعم ملفات CSV و Excel

### ⚙️ **مميزات تقنية متقدمة**
- **إدارة الموارد**: نظام موارد Qt مدمج للأيقونات والصور
- **بناء Release محسن**: إنتاج ملف تنفيذي مستقل
- **دعم متعدد المنصات**: Windows, Linux, macOS
- **نظام إعدادات متقدم**: حفظ وتحميل الإعدادات تلقائياً
- **نظام تسجيل الأحداث**: Logging متقدم للتشخيص

## متطلبات النظام

- Qt6 (Core, Widgets, Sql)
- CMake 3.16 أو أحدث
- مترجم C++17 متوافق
- نظام التشغيل: Windows, Linux, أو macOS

## 🚀 التشغيل السريع

### ⚡ **للمستخدمين - تشغيل فوري**
```bash
# تشغيل التطبيق مباشرة
.\run_application.bat
```

### 🔨 **للمطورين - البناء والتطوير**

#### 1. تثبيت المتطلبات
- **Qt6**: من الموقع الرسمي https://www.qt.io/download
- **CMake 3.16+**: من https://cmake.org/download/
- **مترجم C++17**: MinGW أو Visual Studio

#### 2. البناء السريع (للتطوير)
```bash
# بناء سريع للتطوير
.\build_simple.bat
```

#### 3. البناء للإنتاج (Release)
```bash
# بناء محسن للإنتاج
.\build_release.bat
```

#### 4. البناء اليدوي
```bash
# إنشاء مجلد البناء
mkdir build
cd build

# تكوين المشروع
cmake .. -G "MinGW Makefiles"

# بناء المشروع
cmake --build .

# تشغيل التطبيق
.\bin\SchoolManagementSystem.exe
```

## 🔑 بيانات تسجيل الدخول الافتراضية

```
👤 اسم المستخدم: admin
🔒 كلمة المرور: admin123
```

## 🖼️ لقطات الشاشة

### شاشة تسجيل الدخول الجديدة
- تصميم بلا إطار مع خلفية متدرجة
- كارت شفاف مع تأثيرات الظل
- حقول إدخال حديثة مع أيقونات
- أزرار متدرجة مع تأثيرات hover

### لوحة التحكم المحدثة
- هيدر متدرج مع أيقونات
- جدول الطلاب مع تصميم حديث
- نموذج إدخال البيانات المحسن
- أزرار تفاعلية مع تأثيرات بصرية

## 🎨 التحسينات التقنية

### تصميم الواجهة
- **نظام ألوان متناسق**: تدرجات من الأزرق إلى البنفسجي
- **تأثيرات الظل**: QGraphicsDropShadowEffect للعمق البصري
- **انتقالات سلسة**: CSS transitions للتفاعلات
- **تخطيط متجاوب**: تنظيم ديناميكي للعناصر

### تحسينات الأداء
- **تحديث فوري للبيانات**: عداد الطلاب يتحدث تلقائياً
- **بحث محسن**: نتائج فورية بدون تأخير
- **ذاكرة محسنة**: إدارة أفضل للموارد

## كيفية الاستخدام

### 1. تسجيل الدخول
- أدخل اسم المستخدم وكلمة المرور
- اضغط على "تسجيل الدخول"

### 2. إدارة الطلاب

#### إضافة طالب جديد:
1. املأ النموذج في الجانب الأيمن
2. اضغط على "إضافة طالب"

#### تعديل بيانات طالب:
1. اختر الطالب من الجدول
2. عدل البيانات في النموذج
3. اضغط على "تحديث طالب"

#### حذف طالب:
1. اختر الطالب من الجدول
2. اضغط على "حذف طالب"
3. أكد عملية الحذف

#### البحث عن طالب:
- استخدم مربع البحث في الأعلى
- البحث يتم بالاسم أو البريد الإلكتروني

## هيكل المشروع

```
├── main.cpp              # نقطة البداية
├── Student.h/cpp         # كلاس الطالب
├── DatabaseManager.h/cpp # إدارة قاعدة البيانات
├── LoginForm.h/cpp       # فورم تسجيل الدخول
├── DashboardForm.h/cpp   # لوحة التحكم
├── CMakeLists.txt        # ملف البناء
└── README.md            # هذا الملف
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على:

### جدول المستخدمين (users)
- id: المعرف الفريد
- username: اسم المستخدم
- password: كلمة المرور المشفرة

### جدول الطلاب (students)
- id: المعرف الفريد
- name: اسم الطالب
- email: البريد الإلكتروني
- birth_date: تاريخ الميلاد
- phone: رقم الهاتف
- address: العنوان

## الأمان

- كلمات المرور مشفرة باستخدام SHA-256
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection باستخدام Prepared Statements

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.
