#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QSplashScreen>
#include <QTimer>
#include "ApplicationManager.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));

    // Initialize application manager
    ApplicationManager &appManager = ApplicationManager::getInstance();
    if (!appManager.initialize(&app)) {
        QMessageBox::critical(nullptr, "خطأ", "فشل في تهيئة التطبيق");
        return -1;
    }

    // Set modern application stylesheet
    QString styleSheet = R"(
        * {
            font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
        }

        QWidget {
            font-size: 11pt;
            color: #2c3e50;
        }

        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
        }

        /* Scrollbars */
        QScrollBar:vertical {
            background: #f1f3f4;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #6b46c1);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        /* Tooltips */
        QToolTip {
            background: rgba(44, 62, 80, 0.9);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
        }

        /* Message Boxes */
        QMessageBox {
            background: white;
            border-radius: 15px;
        }

        QMessageBox QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 20px;
            font-weight: bold;
            min-width: 80px;
        }

        QMessageBox QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #6b46c1);
        }

        /* Calendar Widget */
        QCalendarWidget {
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
        }

        QCalendarWidget QToolButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 5px;
            font-weight: bold;
        }

        QCalendarWidget QToolButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #6b46c1);
        }

        QCalendarWidget QAbstractItemView:enabled {
            background: white;
            selection-background-color: #667eea;
            selection-color: white;
        }

        /* Animations */
        QPushButton {
            transition: all 0.3s ease;
        }

        QLineEdit, QTextEdit, QDateEdit {
            transition: border-color 0.3s ease, background-color 0.3s ease;
        }
    )";

    app.setStyleSheet(styleSheet);

    // Show splash screen
    appManager.showSplashScreen();

    // Setup cleanup on exit
    QObject::connect(&app, &QApplication::aboutToQuit, [&appManager]() {
        appManager.shutdown();
    });

    return app.exec();
}
