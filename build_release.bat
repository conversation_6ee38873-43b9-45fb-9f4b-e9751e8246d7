@echo off
echo ========================================
echo    School Management System - Release Builder
echo ========================================
echo.

REM Check if Qt6 is installed
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Qt6 is not found in PATH!
    echo Please install Qt6 and add it to your PATH.
    echo Download from: https://www.qt.io/download
    pause
    exit /b 1
)

REM Check if CMake is installed
where cmake >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: CMake is not found in PATH!
    echo Please install CMake and add it to your PATH.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

echo Qt6 and CMake found!
echo.

REM Create release build directory
if not exist "build-release" (
    echo Creating release build directory...
    mkdir build-release
)

REM Navigate to build directory
cd build-release

echo Configuring project for Release build...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo Error: CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building Release version...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Error: Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.

REM Create distribution directory
if not exist "dist" (
    mkdir dist
)

REM Copy executable
if exist "bin\SchoolManagementSystem.exe" (
    echo Creating distribution package...
    copy "bin\SchoolManagementSystem.exe" "dist\"
    
    REM Copy Qt DLLs
    echo Copying Qt libraries...
    windeployqt "dist\SchoolManagementSystem.exe"
    
    REM Copy additional files
    if exist "..\README.md" copy "..\README.md" "dist\"
    if exist "..\DESIGN_FEATURES.md" copy "..\DESIGN_FEATURES.md" "dist\"
    
    echo.
    echo ========================================
    echo    Release build completed successfully!
    echo ========================================
    echo.
    echo Distribution files are in: build-release\dist\
    echo.
    echo You can now distribute the contents of the dist folder
    echo as a standalone application.
    echo.
    
    REM Ask if user wants to run the release version
    set /p choice="Do you want to run the release version? (y/n): "
    if /i "%choice%"=="y" (
        echo Starting release version...
        start dist\SchoolManagementSystem.exe
    )
) else (
    echo Error: Executable not found!
    echo Please check the build output for errors.
    pause
)

cd ..
echo.
echo Done!
pause
